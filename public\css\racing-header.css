/**
 * Racing Header Styles
 * Carbon fiber header with chrome accents for car enthusiasts
 */

/* Minimal global fixes */
/* Ensure hero section and main content positioning is not affected */
.hero-section,
.main-content,
.container-fluid,
main {
    position: relative;
    z-index: 1;
}

/* Racing Header Background */
.racing-header {
    background:
        /* Carbon fiber weave pattern - more visible */
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #2a2a2a 75%),
        linear-gradient(-45deg, transparent 75%, #2a2a2a 75%),
        /* Carbon fiber dots */
        radial-gradient(circle at 25% 25%, #444 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, #444 1px, transparent 1px),
        /* Base gradient */
        linear-gradient(135deg, #1a1a1a, #2d2d2d, #1a1a1a) !important;
    background-size:
        12px 12px,
        12px 12px,
        12px 12px,
        12px 12px,
        6px 6px,
        6px 6px,
        100% 100%;
    background-position:
        0 0,
        0 0,
        0 0,
        0 0,
        0 0,
        3px 3px,
        0 0;
    border-bottom: 3px solid #c0c0c0 !important;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 100;
    /* Remove overflow: hidden to allow dropdowns to extend outside */
}

/* Override any Bootstrap blue coloring */
.racing-header.navbar,
.racing-header .navbar,
.racing-header.bg-primary {
    background: none !important;
    background-color: transparent !important;
    border: none !important;
}

/* Background Image Support */
.racing-header.has-bg-image {
    background:
        /* Carbon fiber weave overlay - semi-transparent */
        linear-gradient(45deg, rgba(42, 42, 42, 0.6) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(42, 42, 42, 0.6) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(42, 42, 42, 0.6) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(42, 42, 42, 0.6) 75%),
        /* Carbon fiber dots overlay */
        radial-gradient(circle at 25% 25%, rgba(68, 68, 68, 0.5) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(68, 68, 68, 0.5) 1px, transparent 1px),
        /* User background image */
        var(--header-bg-image),
        /* Fallback gradient */
        linear-gradient(135deg, #1a1a1a, #2d2d2d, #1a1a1a);
    background-size:
        12px 12px,
        12px 12px,
        12px 12px,
        12px 12px,
        6px 6px,
        6px 6px,
        cover,
        100% 100%;
    background-position:
        0 0,
        0 0,
        0 0,
        0 0,
        0 0,
        3px 3px,
        center,
        0 0;
    background-repeat:
        repeat,
        repeat,
        repeat,
        repeat,
        repeat,
        repeat,
        no-repeat,
        no-repeat;
}

/* Chrome Accent Lines */
.racing-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #c0c0c0, transparent);
}

.racing-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(192, 192, 192, 0.5), transparent);
}

/* Racing Navbar */
.racing-navbar {
    background: transparent !important;
    padding: 1rem 0;
    position: static !important;
    overflow: visible !important;
}

/* Minimal dropdown positioning fixes */
.racing-header .dropdown-menu {
    position: absolute !important;
    z-index: 1050 !important;
}

/* Racing Brand/Logo */
.racing-brand {
    color: #fff !important;
    font-weight: bold;
    font-size: 1.5rem;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.8);
    letter-spacing: 1px;
    text-decoration: none !important;
}

.racing-brand:hover {
    color: #00ffff !important;
    text-shadow: 
        0 0 20px rgba(0, 255, 255, 0.8),
        0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Racing Navigation Links */
.racing-nav-link {
    color: #c0c0c0 !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.75rem 1rem !important;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    text-decoration: none !important;
}

/* Override any blue link colors */
.racing-header a,
.racing-header .nav-link,
.racing-header .navbar-nav .nav-link {
    color: #c0c0c0 !important;
    text-decoration: none !important;
}

.racing-header a:hover,
.racing-header .nav-link:hover,
.racing-header .navbar-nav .nav-link:hover {
    color: #fff !important;
    text-decoration: none !important;
}

.racing-header a:focus,
.racing-header .nav-link:focus,
.racing-header .navbar-nav .nav-link:focus {
    color: #fff !important;
    text-decoration: none !important;
    outline: none !important;
    box-shadow: none !important;
}

/* Only add spacing between logo and first navigation item (Events) */
.navbar-nav .nav-item:first-child .racing-nav-link {
    margin-left: 2rem !important;
}

.racing-nav-link:hover,
.racing-nav-link:focus {
    color: #fff !important;
    background: linear-gradient(145deg, rgba(192, 192, 192, 0.1), rgba(192, 192, 192, 0.05));
    box-shadow: 
        0 0 15px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Racing Dropdown */
.racing-dropdown-menu {
    background:
        linear-gradient(rgba(30, 30, 30, 0.95), rgba(10, 10, 10, 0.98)),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #2a2a2a 75%),
        linear-gradient(-45deg, transparent 75%, #2a2a2a 75%) !important;
    background-size: cover, 8px 8px, 8px 8px, 8px 8px, 8px 8px !important;
    border: 2px solid #c0c0c0 !important;
    border-radius: 8px !important;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    padding: 0.5rem !important;
    z-index: 1050 !important;
    position: absolute !important;
}

/* Override Bootstrap dropdown colors */
.racing-header .dropdown-menu {
    background:
        linear-gradient(rgba(30, 30, 30, 0.95), rgba(10, 10, 10, 0.98)),
        linear-gradient(45deg, #2a2a2a 25%, transparent 25%),
        linear-gradient(-45deg, #2a2a2a 25%, transparent 25%) !important;
    border: 2px solid #c0c0c0 !important;
}

.racing-dropdown-item {
    color: #c0c0c0 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 4px !important;
    transition: all 0.3s ease;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-dropdown-item:hover,
.racing-dropdown-item:focus {
    background: linear-gradient(145deg, rgba(192, 192, 192, 0.15), rgba(192, 192, 192, 0.1)) !important;
    color: #fff !important;
    text-shadow: 
        0 0 8px rgba(0, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.8);
    transform: translateX(3px);
}

.racing-dropdown-item i {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

/* Racing User Avatar */
.racing-user-avatar {
    border: 2px solid #c0c0c0 !important;
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.racing-user-avatar:hover {
    border-color: #00ffff !important;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Racing Notification Bell */
.racing-notification {
    color: #c0c0c0 !important;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-notification:hover {
    color: #00ffff !important;
    text-shadow: 
        0 0 15px rgba(0, 255, 255, 0.8),
        0 1px 2px rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Racing Menu Button */
.racing-menu-btn {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
    border: 2px solid #c0c0c0 !important;
    border-radius: 8px !important;
    color: #c0c0c0 !important;
    font-weight: bold !important;
    font-size: 0.9rem !important;
    letter-spacing: 1px !important;
    text-transform: uppercase !important;
    padding: 8px 16px !important;
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    animation: racingGlow 2s ease-in-out infinite alternate;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-menu-btn:hover {
    background: linear-gradient(145deg, #2d2d2d, #1a1a1a) !important;
    color: #fff !important;
    border-color: #00ffff !important;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
}

/* Racing Login/Register Buttons */
.racing-login-btn {
    background: transparent !important;
    border: 1px solid #c0c0c0 !important;
    color: #c0c0c0 !important;
    border-radius: 6px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-login-btn:hover {
    background: linear-gradient(145deg, rgba(192, 192, 192, 0.1), rgba(192, 192, 192, 0.05)) !important;
    color: #fff !important;
    border-color: #00ffff !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
    text-shadow: 
        0 0 8px rgba(0, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.8);
}

.racing-register-btn {
    background: linear-gradient(145deg, #c0c0c0, #a0a0a0) !important;
    border: 1px solid #c0c0c0 !important;
    color: #1a1a1a !important;
    border-radius: 6px !important;
    padding: 0.5rem 1rem !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s ease;
    text-shadow: none !important;
}

.racing-register-btn:hover {
    background: linear-gradient(145deg, #00ffff, #00cccc) !important;
    color: #000 !important;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Chrome Dividers */
.racing-divider {
    border-color: rgba(192, 192, 192, 0.3) !important;
    margin: 0.5rem 0 !important;
}

/* Bootstrap Dropdown Override for Racing Header */
.racing-header .dropdown-menu.show {
    display: block !important;
    position: absolute !important;
    z-index: 1050 !important;
    top: 100% !important;
    left: 0 !important;
    transform: none !important;
}

/* Ensure dropdowns appear above other content */
.racing-header .nav-item.dropdown.show {
    z-index: 1051 !important;
    position: relative !important;
}

/* Fix for right-aligned dropdowns */
.racing-header .dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Racing Header Responsive */
@media (max-width: 991.98px) {
    .racing-header {
        padding: 0.5rem 0;
    }

    .racing-brand {
        font-size: 1.3rem;
    }

    .racing-menu-btn {
        font-size: 0.8rem !important;
        padding: 6px 12px !important;
    }
}

/* Additional responsive adjustments for medium screens */
@media (min-width: 768px) and (max-width: 991.98px) {
    .racing-brand {
        font-size: 1.4rem;
    }

    .racing-menu-btn {
        font-size: 0.9rem !important;
        padding: 8px 14px !important;
    }
}

/* Responsive navigation spacing adjustments */
@media (min-width: 992px) and (max-width: 1199.98px) {
    /* Reduce spacing on large tablets/small desktops */
    .navbar-nav .nav-item:first-child .racing-nav-link {
        margin-left: 1.5rem !important;
    }

    .racing-nav-link {
        padding: 0.75rem 0.8rem !important;
    }
}

@media (min-width: 1200px) {
    /* Full spacing on large screens */
    .navbar-nav .nav-item:first-child .racing-nav-link {
        margin-left: 2rem !important;
    }

    .racing-nav-link {
        padding: 0.75rem 1rem !important;
    }
}

/* Comprehensive Blue Color Elimination */
.racing-header,
.racing-header .navbar,
.racing-header .navbar-brand,
.racing-header .nav-link,
.racing-header .dropdown-toggle {
    /* Remove any blue backgrounds */
    background-color: transparent !important;
    /* Remove blue borders */
    border-color: #c0c0c0 !important;
    /* Remove blue text colors */
    color: #c0c0c0 !important;
    /* Remove blue outlines */
    outline-color: #c0c0c0 !important;
}

/* Only apply to racing-specific buttons, not all buttons */
.racing-header .racing-menu-btn,
.racing-header .racing-login-btn,
.racing-header .racing-register-btn {
    background-color: transparent !important;
    border-color: #c0c0c0 !important;
    color: #c0c0c0 !important;
    outline-color: #c0c0c0 !important;
}

/* Specific overrides for active/focus states */
.racing-header .nav-link:focus,
.racing-header .nav-link:hover,
.racing-header .nav-link.active,
.racing-header .nav-link.show,
.racing-header .dropdown-toggle:focus,
.racing-header .dropdown-toggle:hover,
.racing-header .dropdown-toggle.active,
.racing-header .dropdown-toggle.show {
    background-color: rgba(192, 192, 192, 0.1) !important;
    color: #fff !important;
    border-color: #00ffff !important;
    outline: none !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
}

/* Remove Bootstrap's default blue button styling - Only for racing buttons */
.racing-header .racing-menu-btn.btn-primary,
.racing-header .racing-login-btn.btn-primary,
.racing-header .racing-register-btn.btn-primary {
    background: linear-gradient(145deg, #c0c0c0, #a0a0a0) !important;
    border-color: #c0c0c0 !important;
    color: #1a1a1a !important;
}

.racing-header .racing-menu-btn.btn-primary:hover,
.racing-header .racing-login-btn.btn-primary:hover,
.racing-header .racing-register-btn.btn-primary:hover,
.racing-header .racing-menu-btn.btn-primary:focus,
.racing-header .racing-login-btn.btn-primary:focus,
.racing-header .racing-register-btn.btn-primary:focus,
.racing-header .racing-menu-btn.btn-primary:active,
.racing-header .racing-login-btn.btn-primary:active,
.racing-header .racing-register-btn.btn-primary:active {
    background: linear-gradient(145deg, #00ffff, #00cccc) !important;
    border-color: #00ffff !important;
    color: #000 !important;
}



/* Exclude Facebook buttons from racing header styling */
.racing-header .btn-facebook,
.racing-header [class*="facebook"],
.racing-header [id*="facebook"],
.racing-header [onclick*="facebook"],
.racing-header [onclick*="Facebook"] {
    /* Reset to default styling */
    background: #3b5998 !important;
    color: white !important;
    border-color: #3b5998 !important;
    outline-color: #3b5998 !important;
}

.racing-header .btn-facebook:hover,
.racing-header [class*="facebook"]:hover {
    background: #2d4373 !important;
    color: white !important;
    border-color: #2d4373 !important;
}




